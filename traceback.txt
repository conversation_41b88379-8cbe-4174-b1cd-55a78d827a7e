﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/script.rpy", line 27, in script
    n "The sound of the leaves rushing past barely even register as you dash forward, your heart thundering in your chest as you keep your eyes focused ahead of you. Every step seems to jolt you with the force of an explosion."
Exception: Could not find font '/fonts/lazydog.ttf'.

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "game/script.rpy", line 27, in script
    n "The sound of the leaves rushing past barely even register as you dash forward, your heart thundering in your chest as you keep your eyes focused ahead of you. Every step seems to jolt you with the force of an explosion."
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\ast.py", line 2579, in execute
    Say.execute(self)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\ast.py", line 621, in execute
    renpy.exports.say(who, what, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\exports\sayexports.py", line 132, in say
    who(what, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\character.py", line 1455, in __call__
    self.do_display(who, what, cb_args=self.cb_args, dtt=dtt, **display_args)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\character.py", line 1106, in do_display
    display_say(who,
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\character.py", line 803, in display_say
    rv = renpy.ui.interact(mouse='say', type=type, roll_forward=roll_forward)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\ui.py", line 301, in interact
    rv = renpy.game.interface.interact(roll_forward=roll_forward, **kwargs)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\core.py", line 2215, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\core.py", line 2879, in interact_core
    self.draw_screen(root_widget, fullscreen_video, (not fullscreen_video) or video_frame_drawn)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\core.py", line 1381, in draw_screen
    surftree = renpy.display.render.render_screen(
  File "render.pyx", line 486, in renpy.display.render.render_screen
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\screen.py", line 752, in render
    child = renpy.display.render.render(self.child, w, h, st, at)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\layout.py", line 900, in render
    surf = render(child, width, height, cst, cat)
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\display\layout.py", line 1457, in render
    surf = render(child,
  File "render.pyx", line 170, in renpy.display.render.render
  File "render.pyx", line 260, in renpy.display.render.render
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\text\text.py", line 2600, in render
    virtual_layout = Layout(self, width, height, renders, drawable_res=False, size_only=True)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\text\text.py", line 767, in __init__
    glyphs = ts.glyphs(s, self)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\text\text.py", line 340, in glyphs
    fo = font.get_font(self.font, self.size, self.bold, self.italic, 0, self.antialias, self.vertical, self.hinting, layout.oversample, self.shaper, self.instance, self.axis)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\text\font.py", line 745, in get_font
    face = load_face(fn, shaper)
  File "C:\Users\<USER>\Downloads\renpy-8.3.4-sdk\renpy\text\font.py", line 673, in load_face
    raise Exception("Could not find font {0!r}.".format(orig_fn))
Exception: Could not find font '/fonts/lazydog.ttf'.

Windows-10-10.0.19045 AMD64
Ren'Py 8.3.4.24120703
Bandle City Nights 1.0
Mon Feb 10 17:22:37 2025
