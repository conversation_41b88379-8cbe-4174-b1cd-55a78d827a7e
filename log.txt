2025-08-09 17:47:11 UTC
Windows-10-10.0.19045-SP0
Ren'Py 8.4.1.25072401

Early init took 52 ms
Loading error handling took 124 ms
Loading script took 1273 ms
Loading save slot metadata took 35 ms
Loading persistent took 0 ms
Running init code took 85 ms
Loading analysis data took 17 ms
Analyze and compile ATL took 4 ms
Reloading save slot metadata took 162 ms
Backing up script files to 'C:\\Users\\<USER>\\AppData\\Roaming/RenPy/backups\\Bandle City Nights':
Dump and make backups took 61 ms
Cleaning cache took 0 ms
Making clean stores took 0 ms
Initial gc took 51 ms
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Creating interface object took 196 ms
Init translation took 0 ms
Cleaning stores took 1 ms
Load screen analysis took 0 ms
Analyze screens took 21 ms
Save screen analysis took 10 ms
Save pyanalysis took 5 ms
Save bytecode took 24 ms
Running _start took 1 ms
Interface start took 214 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Fullscreen mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce GTX 1070/PCIe/SSE2'
Version: '4.6.0 NVIDIA 560.94'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1920, 1080) physical=(1920, 1080) drawable=(1920, 1080)
Maximum texture size: 4096x4096
